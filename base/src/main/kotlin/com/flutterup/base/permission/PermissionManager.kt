package com.flutterup.base.permission

import android.app.Activity
import android.content.Context
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.FragmentActivity
import com.flutterup.base.BaseApplication
import com.flutterup.base.utils.Timber
import kotlinx.coroutines.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 权限管理器
 * 支持ActivityResultLauncher请求权限，应用内限制请求次数，自定义前置操作等
 */
@Singleton
class PermissionManager @Inject constructor(
    private val permissionStorage: PermissionStorage,
    private val scope: CoroutineScope
) {
    companion object {
        private const val TAG = "PermissionManager"
    }
    
    /**
     * 检查单个权限是否已授权
     */
    fun hasPermission(context: Context, permission: String): Boolean {
        return PermissionUtils.isPermissionGranted(context, permission)
    }
    
    /**
     * 检查多个权限是否都已授权
     */
    fun hasPermissions(context: Context, permissions: List<String>): <PERSON><PERSON><PERSON> {
        return PermissionUtils.arePermissionsGranted(context, permissions)
    }
    
    /**
     * 获取权限状态
     */
    fun getPermissionStatus(activity: Activity, permission: String): PermissionStatus {
        return PermissionUtils.getPermissionStatus(activity, permission)
    }
    
    /**
     * 获取多个权限的状态
     */
    fun getPermissionsStatus(activity: Activity, permissions: List<String>): Map<String, PermissionStatus> {
        return PermissionUtils.getPermissionsStatus(activity, permissions)
    }
    
    /**
     * 请求单个权限
     */
    fun requestPermission(
        activity: FragmentActivity,
        permission: String,
        config: PermissionRequestConfig = PermissionRequestConfig(listOf(permission)),
        callback: PermissionCallback
    ) {
        requestPermissions(activity, listOf(permission), config.copy(permissions = listOf(permission)), callback)
    }
    
    /**
     * 请求多个权限
     */
    fun requestPermissions(
        activity: FragmentActivity,
        permissions: List<String>,
        config: PermissionRequestConfig = PermissionRequestConfig(permissions),
        callback: PermissionCallback
    ) {
        scope.launch {
            try {
                // 检查权限是否可以请求（基于限制类型）
                val requestablePermissions = permissions.filter { permission ->
                    permissionStorage.canRequestPermission(permission, config.limitType)
                }
                
                if (requestablePermissions.isEmpty()) {
                    Timber.d(TAG, "All permissions are limited by request policy")
                    // 所有权限都被限制，返回当前状态
                    val currentResults = permissions.associateWith { permission ->
                        PermissionResult(permission, getPermissionStatus(activity, permission))
                    }
                    callback.onDenied(MultiplePermissionResult(currentResults))
                    return@launch
                }
                
                // 执行前置操作
                if (config.preAction != null) {
                    var shouldContinue = false
                    config.preAction.execute(requestablePermissions) {
                        shouldContinue = it
                    }
                    
                    if (!shouldContinue) {
                        Timber.d(TAG, "Permission request cancelled by pre-action")
                        val currentResults = permissions.associateWith { permission ->
                            PermissionResult(permission, getPermissionStatus(activity, permission))
                        }
                        callback.onDenied(MultiplePermissionResult(currentResults))
                        return@launch
                    }
                }
                
                // 检查已授权的权限
                val grantedPermissions = requestablePermissions.filter { 
                    hasPermission(activity, it) 
                }
                val deniedPermissions = requestablePermissions - grantedPermissions.toSet()
                
                if (deniedPermissions.isEmpty()) {
                    // 所有权限都已授权
                    val results = permissions.associateWith { permission ->
                        PermissionResult(permission, PermissionStatus.GRANTED)
                    }
                    callback.onGranted(MultiplePermissionResult(results))
                    return@launch
                }
                
                if (config.showRationale) {
                    callback.onShowRationale(
                        deniedPermissions,
                        proceed = { 
                            performPermissionRequest(activity, deniedPermissions, config, callback)
                        },
                        cancel = {
                            val currentResults = permissions.associateWith { permission ->
                                PermissionResult(permission, getPermissionStatus(activity, permission))
                            }
                            callback.onDenied(MultiplePermissionResult(currentResults))
                        }
                    )
                } else {
                    performPermissionRequest(activity, deniedPermissions, config, callback)
                }
                
            } catch (e: Exception) {
                Timber.e(TAG, "Error requesting permissions", e)
                val currentResults = permissions.associateWith { permission ->
                    PermissionResult(permission, getPermissionStatus(activity, permission))
                }
                callback.onDenied(MultiplePermissionResult(currentResults))
            }
        }
    }
    
    /**
     * 执行实际的权限请求
     */
    private fun performPermissionRequest(
        activity: FragmentActivity,
        permissions: List<String>,
        config: PermissionRequestConfig,
        callback: PermissionCallback
    ) {
        try {
            // 记录权限请求
            permissions.forEach { permission ->
                permissionStorage.recordPermissionRequest(permission, config.limitType)
            }
            
            // 创建权限请求启动器
            val launcher = activity.activityResultRegistry.register(
                "permission_request_${System.currentTimeMillis()}",
                ActivityResultContracts.RequestMultiplePermissions()
            ) { grantResults ->
                handlePermissionResult(activity, permissions, grantResults, config, callback)
            }
            
            // 启动权限请求
            launcher.launch(permissions.toTypedArray())
            
        } catch (e: Exception) {
            Timber.e(TAG, "Error performing permission request", e)
            val currentResults = config.permissions.associateWith { permission ->
                PermissionResult(permission, getPermissionStatus(activity, permission))
            }
            callback.onDenied(MultiplePermissionResult(currentResults))
        }
    }
    
    /**
     * 处理权限请求结果
     */
    private fun handlePermissionResult(
        activity: Activity,
        requestedPermissions: List<String>,
        grantResults: Map<String, Boolean>,
        config: PermissionRequestConfig,
        callback: PermissionCallback
    ) {
        val results = config.permissions.associateWith { permission ->
            if (requestedPermissions.contains(permission)) {
                val isGranted = grantResults[permission] == true
                val status = if (isGranted) {
                    PermissionStatus.GRANTED
                } else {
                    getPermissionStatus(activity, permission)
                }
                PermissionResult(permission, status)
            } else {
                PermissionResult(permission, getPermissionStatus(activity, permission))
            }
        }
        
        val multipleResult = MultiplePermissionResult(results)
        
        if (multipleResult.allGranted) {
            callback.onGranted(multipleResult)
        } else {
            callback.onDenied(multipleResult)
        }
        
        Timber.d(TAG, "Permission request completed: ${multipleResult.results}")
    }
    
    /**
     * 打开应用设置页面
     */
    fun openAppSettings(context: Context) {
        PermissionUtils.openAppSettings(context)
    }
    
    /**
     * 清除权限请求记录
     */
    fun clearPermissionRecord(permission: String) {
        permissionStorage.clearPermissionRecord(permission)
    }
}
