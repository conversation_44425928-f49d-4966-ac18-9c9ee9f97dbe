plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.ksp)
}

apply(from = "${rootDir}/gradles/buildBase.gradle")
apply(from = "${rootDir}/gradles/buildHilt.gradle")


android {
    namespace = "com.flutterup.gifts"
}

dependencies {
    implementation(project(":base"))
    implementation(project(":network"))

    implementation(libs.androidx.room)
    implementation(libs.androidx.room.ktx)
    ksp(libs.androidx.room.compiler)
}