package com.flutterup.gifts

import android.content.Context
import com.flutterup.base.AppDirs
import com.flutterup.base.Dirs
import com.flutterup.gifts.database.GiftDatabase
import com.flutterup.gifts.entity.GiftCacheEntity
import com.flutterup.gifts.entity.GiftResourceInfo
import com.flutterup.network.AppDispatchers
import com.flutterup.network.Dispatcher
import com.flutterup.network.DownloadApiService
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import javax.inject.Inject


class GiftCacheManager @Inject constructor(
    @ApplicationContext private val context: Context,
    @Dispatcher(AppDispatchers.IO) private val dispatchers: CoroutineDispatcher,
    @Dirs(AppDirs.CACHE) private val fileDir: File,
    private val database: GiftDatabase,
    private val downloadApiService: DownloadApiService
) {
    companion object {
        private const val IMAGE_SUFFIX = ".png"
        private const val GIF_SUFFIX = ".gif"
        private const val MP4_SUFFIX = ".mp4"
        private const val IMAGE_DIR = "gift/image"
        private const val GIF_DIR = "gift/gif"
        private const val MP4_DIR = "gift/mp4"
    }


    suspend fun cacheGiftIfNeeded(info: GiftResourceInfo) {
        val cachedGift = database.giftCacheDao().getGift(info.giftId)

        val imageFile = getGiftImageFile(info.giftId)
        val gifFile = getGiftGifFile(info.giftId)
        val mp4File = getGiftMp4File(info.giftId)

        var needDownloadImage = true
        var needDownloadGif = true
        var needDownloadMp4 = true

        if (cachedGift != null) {
            if (cachedGift.version >= info.version) { // 版本相同，检查文件是否存在
                if (cachedGift.imageHash == info.imageHash && imageFile.exists()) {
                    needDownloadImage = false
                }
                if (cachedGift.gifHash == info.gifHash && gifFile.exists()) {
                    needDownloadGif = false
                }
                if (cachedGift.mp4Hash == info.mp4Hash && mp4File.exists()) {
                    needDownloadMp4 = false
                }
            }
        }

        if (needDownloadImage && info.image != null) {
            downloadToFile(info.image, imageFile)
        }
        if (needDownloadGif && info.gif != null) {
            downloadToFile(info.gif, gifFile)
        }
        if (needDownloadMp4 && info.mp4 != null) {
            downloadToFile(info.mp4, mp4File)
        }

    }

    private suspend fun downloadToFile(url: String, destFile: File): File? {
        return withContext(Dispatchers.IO) {
            try {
                destFile.parentFile?.mkdirs()

                val response = downloadApiService.download(url)

                response.byteStream().use { input ->
                    FileOutputStream(destFile).use { output ->
                        input.copyTo(output)
                    }
                }
                response.close()
                destFile
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }
    }

    private fun getGiftImageFile(giftId: String, suffix: String = IMAGE_SUFFIX): File {
        return File(fileDir, "$IMAGE_DIR/$giftId$suffix")
    }

    private fun getGiftGifFile(giftId: String, suffix: String = GIF_SUFFIX): File {
        return File(fileDir, "$GIF_DIR/$giftId$suffix")
    }

    private fun getGiftMp4File(giftId: String, suffix: String = MP4_SUFFIX): File {
        return File(fileDir, "$MP4_DIR/$giftId$suffix")
    }

    private fun insertGifResource(info: GiftResourceInfo, imageFile: File, gifFile: File, mp4File: File) {
        val giftCacheEntity = GiftCacheEntity(
            giftId = info.giftId,
            name = info.name,
            imagePath = imageFile.absolutePath,
            gifPath = gifFile.absolutePath,
            mp4Path = mp4File.absolutePath,
            imageHash = info.imageHash,
            gifHash = info.gifHash,
            mp4Hash = info.mp4Hash,
            version = info.version
        )

        database.giftCacheDao().insertGift(giftCacheEntity)
    }
}
